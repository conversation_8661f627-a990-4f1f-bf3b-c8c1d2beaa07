import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { getMenuNameResp, MenuItem } from '../../shared/models/menu.model';
import { getMenuListResp, menuItem } from '../interface/menu.interface';

@Injectable({
  providedIn: 'root',
})
export class MenuItemService {
  private menuItemApi: string = '/api/MenuItem';
  webSiteId: string = localStorage.getItem('webSiteId') as string;
  lang: string = localStorage.getItem('lang') || 'zh';
  constructor(private http: HttpClient) {}

  /**
   * 取得選單列表
   * @returns
   */
  getMenuList(): Observable<menuItem[]> {
    return this.http.get<any>(`api/MenuItem/tree/${this.webSiteId}`, {
      params: {
        lang: this.lang,
      },
    });
  }

  /**
   * 取得次選單內容
   * @param menuItemId
   * @returns
   */
  getMenuItem(menuItemId: string): Observable<menuItem> {
    return this.http.get<menuItem>(`api/MenuItem/${menuItemId}`);
  }
  
  getTabList(id: string): Observable<MenuItem> {
    return this.http.get<MenuItem>(`api/MenuItem/${id}`);
  }

  /**
   * 取得選單內容主標題與次標題
   * @param menuItemId
   * @returns
   */
  getMenuName(menuItemId: string): Observable<getMenuNameResp> {
    return this.http.get<getMenuNameResp>(
      `api/MenuItem/menuitemName?childMenuitem=${menuItemId}`
    );
  }
}
