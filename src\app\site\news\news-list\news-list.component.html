<main class="main-layout">
    <div class="pages-layout" [formGroup]="form">
        <h2 class="pages-title">最新消息</h2>
        <!--內容放置區-->
        <div class="pages-content">
            <div class="pages-content-lsit">
                <!--查詢條件-->
                <div class="form-module-layout">
                    <div class="form-module-item">
                        <p class="form-module-title">消息分類</p>
                        <!-- 下拉選單元件 START-->
                        <div>
                            <span class="select-control w-full">
                                <select formControlName="newsType">
                                    @for (item of typeList; track item) {
                                    <option [value]="item.typeValue">{{item.typeName}}</option>
                                    }
                                </select>
                            </span>
                        </div>
                        <!-- 下拉選單元件 END-->
                    </div>
                    <div class="form-module-item">
                        <p class="form-module-title"></p>
                        <!-- 搜尋元件元件 START-->
                        <div class="module-input">
                            <input type="search" class="form-control w-full" formControlName="keyWord"
                                placeholder="請輸入關鍵字" title="請輸入關鍵字">
                            <input class="btn-list btn-primary-color" value="搜尋" type="button" (click)="search()">
                        </div>
                        <!-- 搜尋元件元件 END-->
                    </div>
                </div>
                <!--列表-->
                <div class="news-pic-layout">
                    <div class="news-pic-cont">
                        <!--01-->
                        @for (item of newsList; track item) {
                        <article class="news-pic-cont-item">
                            <div class="news-pic-cont-item-img">
                                <img [src]="item.coverUrl" [alt]="item.coverDescription">
                            </div>
                            <div class="news-pic-cont-item-list">
                                <span className="news-pic-cont-item-date">{{item.startTime}}</span>
                                <h3 class="news-pic-cont-item-title">{{item.title}}</h3>
                                <p class="news-pic-cont-item-text">
                                    {{item.description}}
                                </p>
                            </div>
                        </article>
                        }
                    </div>
                </div>
                @if(newsList.length < 1){ <div class="not-found">
                    <span>查無資料</span>
            </div>
            }
            @if(newsList.length > 0){
            <app-paginator [pageSize]="pageSize" [nowPage]="nowPage" [totalRecords]="totalCount"
                [pageShowCount]="pageShowCount" currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
                (clickPageEvent)="getPageFromPaginator($event)"
                (pageSizeChangeEvent)="getPageSizeFromPaginator($event)"></app-paginator>
            }
        </div>
    </div>
    </div>
</main>