import { Component } from '@angular/core';
import { environment } from '../../../environments/environment';
import { ShareService } from '../../core/services/share.service';
import {
  getTypeListResp,
  typeItem,
} from '../../core/interface/share.interface';
import { HomeService } from '../../core/services/home.service';
import {
  getNewsListWithHomeResp,
  newsItem,
} from '../../core/interface/news.interface';

@Component({
  selector: 'app-home',
  standalone: false,

  templateUrl: './home.component.html',
  styleUrl: './home.component.scss',
})
export class HomeComponent {
  language: string = '';
  apiurl: string = environment.apiUrl;
  selectNewsType: string = '';
  typeList: typeItem[] = [];

  newsList: newsItem[] = [];

  constructor(
    private shareService: ShareService,
    private homeService: HomeService
  ) {}

  ngOnInit(): void {
    this.getTypeList();
  }

  getTypeList() {
    this.shareService.getTypeList('前台最新消息').subscribe({
      next: (resp: getTypeListResp) => {
        this.typeList = resp.data;
        if (this.typeList && this.typeList.length > 0) {
          this.selectNewsType = this.typeList[0].typeValue;
          this.selectNewsList(this.selectNewsType);
        }
      },
      error: () => {},
    });
  }

  selectNewsList(type: string) {
    this.selectNewsType = type;
    this.homeService.getNewsListWithHome(type).subscribe({
      next: (resp: getNewsListWithHomeResp) => {
        console.log(resp);
        this.newsList = resp.data.data;
        console.log(this.newsList);
      },
      error: () => {},
    });
  }
}
