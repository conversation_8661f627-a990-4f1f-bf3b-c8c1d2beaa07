import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { getNewsListWithHomeResp } from '../interface/news.interface';

export interface getTodayScheduleListResp {
  code: number;
  message: string;
  data: {
    date: string;
    weekName: string;
    sportData: sportDataItem[];
  };
}

export interface sportDataItem {
  imageUrl: string;
  sportType: string;
  sportClass: string;
  cardLink: string;
}

@Injectable({
  providedIn: 'root',
})
export class HomeService {
  constructor(private httpClient: HttpClient) {}
  webSiteId: string = localStorage.getItem('webSiteId')!;

  getTodayScheduleList(): Observable<getTodayScheduleListResp> {
    return this.httpClient.get<getTodayScheduleListResp>(
      'api/New_News/HomePageCompetitnioData',
      {
        params: {
          websiteId: this.webSiteId,
        },
      }
    );
  }

  getNewsListWithHome(type: string): Observable<getNewsListWithHomeResp> {
    return this.httpClient.get<getNewsListWithHomeResp>('api/New_News/ListNewsResponseHomepage', {
      params: {
        websiteId: this.webSiteId,
        type: type,
        lang: localStorage.getItem('lang') || 'zh',
      },
    });
  }
}
