import { MenuItem } from './../../shared/models/menu.model';
import { newsListReq, resModel } from './../../shared/models/news.model';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { PagingOfNews } from '../../shared/models/pagingOfNews.model';
import { Observable } from 'rxjs';
import { palaSportDatas } from '../../shared/models/sport.model';
import { getNewsListReq, getNewsListResp } from '../interface/news.interface';

export interface getCultureTableListReq {
  tagId: string;
  date: string;
  tag: string;
}

@Injectable({
  providedIn: 'root',
})
export class NewsService {
  constructor(private http: HttpClient) {}

  getNewsList(req: getNewsListReq): Observable<getNewsListResp> {
    return this.http.post<getNewsListResp>('api/New_News/New_NewsList', req);
  }
}
