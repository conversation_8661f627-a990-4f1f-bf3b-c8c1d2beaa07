// Scss Document
.form-module{
	&-layout{
		margin: 0;
		padding: 0 0 20px;
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-start;
		align-items: stretch;
		gap: 1.5rem;
	}
	&-title{
		margin: 0;
		padding: 0 0 5px;
		font-weight: bold;
		font-size: 1.125em;
		&-imp{
			color: #b82e0f;
		}
	}
	&-item{
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		flex-basis: 0;
        flex-grow: 1;
        max-width: 100%;
		&-full{
			flex: 0 0 100%;
		}
		&-list{
			display: flex;
			justify-content: flex-start;
			flex-wrap: nowrap;
			align-items: center;
			flex-basis: 0;
			flex-grow: 1;
			width: 100%;
			gap: 0.5rem;
		}
		.align-c{
			text-align: center;
		}
	}
	&-input-text{
		position: relative;
		span{
			display: inline-flex;
			position: absolute;
			padding: 8px 10px;
			font-size: 1.125em;
			line-height: 1.6;
		}
		.form-control{
			padding-left: 60px;
		}
	}
}

.w-full{
	width: 100%;
}

//原角板本
//輸入框
.input {
	font-size: 1rem;
	line-height: 1.625;
	letter-spacing: 0.125rem;
	padding: 9px 2px 9px 16px;
	border-radius: 40px;
	font-weight: 700;
	width: 260px;
	max-width: 100%;
	-webkit-appearance: none;
	border: 1px solid #f3f3f5;
	background: #f3f3f5;

	&.text-center {
		text-indent: 0.125rem;
	}

	&[type="search"]::-webkit-search-cancel-button {
		-webkit-appearance: none;
		height: 32px;
		width: 32px;
		border-radius: 50%;
		// background: url("../..assets/img/icon/icon-cross-black.svg") center center no-repeat;
		cursor: pointer;
	}

	&.bg-white {
		background: #fff;
	}

	&:focus {
		padding: 6px 0 6px 13px;
		border: 4px solid #3367ac;
	}

	&.style-dark {
		background: rgba(243, 243, 245, 0.2);
		border: 1px solid #fff;

	&:focus {
	  padding: 6px 0 6px 13px;
	  border: 4px solid #3367ac;
	}

	&::-moz-placeholder, &::placeholder {
	  color: rgba(255, 255, 255, 0.5);
	}
	}
}

.module-input{
	display: flex;
	flex-wrap: nowrap;
	justify-content: flex-start;
	align-items: center;
	gap: 0.5rem;
}


//下拉選單
.module-select {
	position: relative;
	.block-select-bg {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		flex-direction: row;
		position: absolute;
		pointer-events: none;
		-webkit-user-select: none;
		-moz-user-select: none;
		user-select: none;
		z-index: 0;
		left: 0;
		right: 0;
		top: 0;
		height: 44px;
		width: 100%;
		border-radius: 22px;
		&-rect {
			height: 44px;
			flex-basis: 0;
			flex-grow: 1;
			max-width: 100%;
			border-radius: 22px;
			background-color: #f3f3f5;
			color: #000;
		  }
	}

	select.select {
		position: relative;
		font-size: 1rem;
		line-height: 1.625;
		letter-spacing: 0.125rem;
		font-weight: 700;
		height: 44px;
		background: transparent;
		padding-right: 52px;
		cursor: pointer;
		padding-left: 22px;
		line-height: 44px;
		-webkit-appearance: none;
		-moz-appearance: none;
		appearance: none;
		z-index: 1;
		position: relative;
		  border: 0;

		&.text-center {
		  text-indent: 0.125rem;
		}

		&:focus {
		  border: 4px solid #3367ac;
		  border-radius: 44px;
		  padding-right: 48px;
		  padding-left: 18px;
		  line-height: 36px;
		}

		option {
		  background: #f3f3f5;
		  -webkit-appearance: none;
		  -moz-appearance: none;
		  appearance: none;
		}
	}
}

//按鈕
.button-dot {
	width: 44px;
    height: 44px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background-color 0.1s ease;
	border-radius: 22px;
	background-color: #f3f3f5;
	color: #000;
	border: 0;
	font-weight: bold;
	* {
		pointer-events: none;
		-webkit-user-select: none;
		-moz-user-select: none;
		user-select: none;
	}
	&:hover {
		background-color: #b3afbf;
	}
	&:active, &.is-active {
		background-color: #4d4b5d;
		color: #fff;
	}
	&:disabled, &.is-disabled {
		opacity: 0.3;
		pointer-events: none;
	}
}


@media screen and (max-width: 768px){
	.form-module{
		&-layout{
			flex-direction: column;
		}
		&-item{
			&-list{
			flex-wrap: wrap;
			}
		}
		
	}
}


