import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class UtilService {
  constructor(
    private httpClient: HttpClient,
    private metaService: Meta,
    private translateService: TranslateService,
    private titleService: Title
  ) {}

  checkDevice(): boolean {
    const userAgent = navigator.userAgent.toLowerCase();
    const userAgentData = (navigator as any).userAgentData;
    // 取得平台資訊，若 userAgentData 支援則使用，否則回退到 userAgent 判斷
    const platform = userAgentData?.platform || userAgent;
    const isMacLike = platform.includes('mac');
    const isMobileSafari =
      /iphone|ipod|ipad/i.test(userAgent) ||
      (isMacLike && 'ontouchstart' in window);

    const isAndroid = userAgent.includes('android');
    const isDevice = userAgent.includes('tw.wmg2025.app');
    const isIPhone = /iphone/i.test(userAgent) || (isMacLike && isMobileSafari);
    const isIPad =
      /ipad/i.test(userAgent) || (isMacLike && 'ontouchstart' in window);

    // 判斷是否為行動裝置
    const isMobileDevice = isAndroid || isIPhone || isIPad || isDevice;

    return !isMobileDevice; // 若為 PC 則回傳 true，否則回傳 false
  }

  sendMewsLetter(
    email: string
  ): Observable<{ success: boolean; message: string }> {
    return this.httpClient.post<{ success: boolean; message: string }>(
      '/api/WMGauth/NewsLetters',
      {
        email: email,
        languageCode: sessionStorage.getItem('language'),
      }
    );
  }

  setOg(title: string) {
    this.metaService.updateTag({
      property: 'og:title',
      content: title,
    });
    this.metaService.updateTag({
      property: 'og:description',
      content: title,
    });
    this.metaService.updateTag({
      property: 'twitter:card',
      content: 'summary',
    });
    this.metaService.updateTag({
      property: 'twitter:title',
      content: title,
    });
    this.metaService.updateTag({
      property: 'twitter:description',
      content: title,
    });
  }

  setOgTitle(title: string) {
    this.translateService.get('2025title').subscribe((titleName: string) => {
      this.metaService.updateTag({
        property: 'title',
        content: titleName + '-' + title,
      });
      this.titleService.setTitle(titleName + '-' + title);
      this.metaService.updateTag({
        property: 'og:title',
        content: titleName + '-' + title,
      });
    });
  }

  getWebSiteID(): string {
    return localStorage.getItem('webSiteId') || '';
  }
}
