import { Component, OnInit } from '@angular/core';
import { MenuItemService } from '../../../core/services/menu-item.service';
import {
  getMenuListResp,
  menuItem,
} from '../../../core/interface/menu.interface';
import { Router } from '@angular/router';

@Component({
  selector: 'app-header',
  standalone: false,
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss',
})
export class HeaderComponent implements OnInit {
  menuList: menuItem[] = [];
  menuSubList: any = [];
  isPhoneMenuOpenStatus: boolean = false;
  menuId: string = '';
  lang: string = sessionStorage.getItem('language') || 'zh';
  constructor(
    private menuItemService: MenuItemService,
    private router: Router
  ) {}
  ngOnInit(): void {
    window.addEventListener('resize', () => this.checkDevice());
    this.getMenu();
  }
  getMenu() {
    this.menuItemService.getMenuList().subscribe({
      next: (resp: menuItem[]) => {
        this.menuList = resp;
      },
      error: () => {},
    });
  }

  clickMenu(item: menuItem) {
    if (item.type !== 'Folder') {
      this.menuId = '';
      //todo go page
      this.router.navigate([`/${item.type}/`], {
        queryParams: {
          menuItemId: item.id,
        },
      });
      return;
    }
    if (this.menuId === item.id) {
      this.menuId = '';
      return;
    } else {
      this.menuId = item.id;
      this.getSubMenu(item);
    }
    // this.router.navigate([''])
  }

  goPage(item: menuItem) {
    if (item.type !== 'Folder') {
      //todo go page
      this.router.navigate([`/${item.type}/`], {
        queryParams: {
          menuItemId: item.id,
        },
      });
      return;
    }
  }

  getSubMenu(item: menuItem) {
    this.menuItemService.getMenuItem(item.id).subscribe({
      next: (resp: menuItem) => {
        this.menuSubList = resp.inverseParent;
      },
      error: () => {},
    });
  }

  checkDevice() {
    const isDesktop = window.innerWidth >= 1024; // 你定義的桌機寬度門檻
    if (isDesktop) {
      this.isPhoneMenuOpenStatus = false; // 強制關閉手機選單
    }
  }
}
