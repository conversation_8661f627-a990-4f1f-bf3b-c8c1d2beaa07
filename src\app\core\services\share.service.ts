import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { getTypeListResp } from '../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class ShareService {
  constructor(private httpClient: HttpClient) {}

  getLang(): string {
    return localStorage.getItem('lang') || 'zh';
  }

  getTypeList(type: string): Observable<getTypeListResp> {
    return this.httpClient.get<getTypeListResp>(
      'api/Manage/New_News/GetTypeList',
      {
        params: {
          type: type,
          lang: localStorage.getItem('lang') || 'zh',
        },
      }
    );
  }
}
